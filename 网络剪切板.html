<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 超级美化网络剪切板 - 云端智能管理</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📋</text></svg>">
    <style>
        /* 🎨 简洁现代配色方案 - 清爽优雅 */
        :root {
            --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            --bg-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            --bg-tertiary: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --bg-quaternary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --bg-glass: rgba(255, 255, 255, 0.8);
            --bg-glass-hover: rgba(255, 255, 255, 0.9);
            --card-bg: rgba(255, 255, 255, 0.9);
            --card-bg-dark: rgba(30, 41, 59, 0.9);
            --text-color: #1e293b;
            --text-light: #ffffff;
            --text-medium: #475569;
            --text-dim: #64748b;
            --border-color: rgba(59, 130, 246, 0.15);
            --card-border: rgba(148, 163, 184, 0.2);
            --highlight-color: #3b82f6;
            --button-primary: #3b82f6;
            --button-danger: #ef4444;
            --button-success: #10b981;
            --button-info: #06b6d4;
            --button-warning: #f59e0b;
            --button-secondary: #6366f1;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            --shadow-hover: 0 8px 20px rgba(0, 0, 0, 0.1);
            --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.2);
            --border-radius: 12px;
            --border-radius-small: 8px;
            --button-radius: 8px;
            --card-padding: 20px;
            --transition: all 0.3s ease;
            --backdrop-blur: blur(10px);
            --glass-border: 1px solid rgba(255, 255, 255, 0.3);
            --gradient-text: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        /* 🌟 全局样式重置 */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 🌌 现代化渐变背景 */
        body {
            background: var(--bg-primary);
            background-attachment: fixed;
            padding: 20px;
            margin: 0;
            max-width: 1200px;
            margin: 0 auto;
            color: var(--text-color);
            min-height: 100vh;
            font-size: 16px;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* 简洁背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
            z-index: -1;
        }

        /* 🎨 简洁优雅标题 */
        h1 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--highlight-color);
            letter-spacing: -0.01em;
            position: relative;
            margin-top: 20px;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--bg-secondary);
            border-radius: 2px;
        }

        /* 🎪 超级美化的头部区域 */
        .header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
            position: relative;
            z-index: 10;
        }

        /* 🌙 超级美化的切换按钮 */
        .dark-mode-toggle {
            background: var(--bg-glass);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            width: 56px;
            height: 56px;
            cursor: pointer;
            font-size: 24px;
            transition: var(--transition);
            box-shadow: var(--shadow);
            backdrop-filter: var(--backdrop-blur);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .dark-mode-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-secondary);
            opacity: 0;
            transition: var(--transition);
            border-radius: 50%;
        }

        .dark-mode-toggle:hover {
            transform: translateY(-4px) scale(1.1);
            box-shadow: var(--shadow-hover), var(--shadow-glow);
            border-color: var(--highlight-color);
        }

        .dark-mode-toggle:hover::before {
            opacity: 1;
        }

        .dark-mode-toggle:hover span {
            color: var(--text-light);
            z-index: 1;
            position: relative;
        }

        /* 🎨 现代化玻璃卡片样式 */
        .snippet {
            margin-bottom: 20px;
            padding: var(--card-padding);
            background: var(--card-bg);
            border: var(--glass-border);
            border-radius: var(--border-radius);
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            transition: var(--transition);
            box-shadow: var(--shadow);
            font-size: 16px;
            line-height: 1.5;
            backdrop-filter: var(--backdrop-blur);
            position: relative;
            overflow: hidden;
            cursor: grab;
        }

        /* 🎯 拖拽状态样式 */
        .snippet.dragging {
            opacity: 0.5;
            transform: rotate(5deg) scale(1.05);
            cursor: grabbing;
            z-index: 1000;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .snippet.drag-over {
            border-color: var(--highlight-color);
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
        }

        .drag-handle {
            cursor: grab;
            padding: 5px;
            margin-right: 10px;
            color: var(--text-dim);
            font-size: 18px;
            transition: var(--transition);
            border-radius: 4px;
        }

        .drag-handle:hover {
            color: var(--highlight-color);
            background: rgba(59, 130, 246, 0.1);
        }

        .drag-handle:active {
            cursor: grabbing;
        }



        .snippet:hover {
            border-color: var(--highlight-color);
            box-shadow: var(--shadow-hover);
            background: var(--bg-glass-hover);
            transform: translateY(-1px);
        }

        .snippet-content {
            flex-grow: 1;
            text-align: left;
            padding: 10px;
            word-break: break-word;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
        }

        .snippet-content:hover {
            background: rgba(0, 123, 255, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .snippet-content:hover::after {
            content: "点击智能复制";
            position: absolute;
            top: 5px;
            right: 8px;
            font-size: 10px;
            color: var(--highlight-color);
            background: rgba(0, 123, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            opacity: 0.8;
            pointer-events: none;
            z-index: 10;
        }

        .snippet-content:active {
            transform: translateY(0);
            background: rgba(0, 123, 255, 0.1);
        }

        .actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 统一按钮基础样式 */
        .copy-button, .delete-button, .edit-button, .pin-button, .toggle-expand-button,
        .add-button, .add-section-btn, .toggle-all-expand-btn,
        .export-button, .import-button, .batch-controls button, .sort-options button,
        .search-container button, .advanced-toggle-btn {
            padding: 10px 16px;
            border: none;
            border-radius: var(--button-radius);
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: var(--shadow);
            min-height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            background: var(--bg-glass);
            color: var(--text-color);
            border: var(--glass-border);
        }

        /* 通用按钮悬停效果 */
        .copy-button:hover, .delete-button:hover, .edit-button:hover, .pin-button:hover,
        .toggle-expand-button:hover, .add-button:hover, .add-section-btn:hover,
        .sort-options button:hover, .search-container button:hover,
        .advanced-toggle-btn:hover, .export-button:hover, .import-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .add-section-input {
            flex-grow: 1;
            padding: 6px 10px;
            border: 1px solid var(--card-border);
            border-radius: 6px;
            font-size: 13px;
        }

        .add-section-input, #searchInput, .section-selector {
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: var(--button-radius);
            font-size: 14px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            min-height: 40px;
            box-sizing: border-box;
        }

        .add-section-input:focus, #searchInput:focus, .section-selector:focus {
            outline: none;
            border-color: var(--highlight-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        .toggle-all-expand-btn {
            background: linear-gradient(135deg, #007aff, #5856d6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--button-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-all-expand-btn:hover {
            background: linear-gradient(135deg, #5856d6, #4b4acf);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(88, 86, 214, 0.3);
        }

        /* 简洁标签样式 */
        .section-tab {
            padding: 8px 16px;
            background: rgba(59, 130, 246, 0.1);
            color: var(--highlight-color);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-tab:hover {
            background: rgba(59, 130, 246, 0.15);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .section-tab.active {
            background: var(--button-primary);
            color: white;
            box-shadow: var(--shadow);
        }

        .add-section-container {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            align-items: center;
        }

        .add-section-btn {
            padding: 10px 16px;
            background: var(--button-success);
            color: white;
            border: none;
            border-radius: var(--button-radius);
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            min-height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }

        .usage-tips {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            padding: 6px 12px;
            background: rgba(0, 123, 255, 0.05);
            border-radius: 16px;
            color: rgba(0, 123, 255, 0.8);
            font-size: 12px;
            max-width: 300px;
            margin-left: auto;
            margin-right: auto;
        }

        .compact-tips {
            margin: 10px 0;
            padding: 8px 12px;
            background: rgba(74, 144, 226, 0.08);
            border-radius: 8px;
            font-size: 12px;
            color: var(--text-medium);
            line-height: 1.4;
        }

        .batch-management {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid var(--card-border);
        }

        .batch-management h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .batch-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
        }

        #batchCounter {
            color: var(--text-medium);
            font-size: 11px;
            white-space: nowrap;
        }

        .expand-control-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }

        .toggle-all-expand-btn {
            background: linear-gradient(135deg, #007aff, #5856d6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--button-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-area, .search-container, .sort-options, .export-import-container {
            margin: 20px auto;
            width: 90%;
            text-align: center;
        }

        textarea {
            width: 90%;
            padding: 20px;
            margin-bottom: 20px;
            border: var(--glass-border);
            border-radius: var(--border-radius);
            font-size: 16px;
            resize: vertical;
            min-height: 140px;
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            transition: var(--transition);
            font-family: inherit;
            line-height: 1.6;
            box-shadow: var(--shadow);
        }

        textarea:focus {
            outline: none;
            border-color: var(--highlight-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), var(--shadow-hover);
            background: var(--bg-glass-hover);
            transform: translateY(-2px);
        }

        textarea::placeholder {
            color: var(--text-dim);
            font-style: italic;
        }

        .add-button {
            padding: 12px 24px;
            background-color: var(--button-primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            cursor: pointer;
        }

        .add-button {
            background: linear-gradient(135deg, var(--button-primary), #0066cc);
            color: white;
            font-size: 15px;
        }

        .add-button:hover {
            background: linear-gradient(135deg, #0066cc, #0056b3);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 122, 255, 0.3);
        }

        .advanced-features {
            display: none;
        }

        .tags-input-container {
            margin-bottom: 10px;
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 10px;
        }

        .tag {
            display: inline-block;
            background: rgba(0, 123, 255, 0.1);
            color: var(--highlight-color);
            padding: 4px 12px;
            border-radius: 20px;
            margin: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .dark-mode {
            --bg-color: #1a1a1a;
            --text-color: #f5f5f7;
            --card-bg: rgba(45, 45, 47, 0.8);
            --border-color: rgba(255, 255, 255, 0.1);
            --card-border: rgba(255, 255, 255, 0.05);
        }

        .dark-mode body {
            background: linear-gradient(135deg, #1a1a1a, #2d2d30);
        }

        /* 分区选择器样式 */
        .section-selector {
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid var(--card-border);
            font-size: 12px;
            background: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .section-selector:hover {
            border-color: var(--highlight-color);
        }

        .section-selector:focus {
            outline: none;
            border-color: var(--highlight-color);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }

        /* 高亮被复制的文本片段 */
        .highlighted-text {
            background: linear-gradient(90deg, rgba(0, 123, 255, 0.3), rgba(0, 123, 255, 0.1));
            color: var(--highlight-color);
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: 600;
            box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }

        /* 悬停时的文本片段高亮 */
        .hover-segment {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
            padding: 1px 3px;
            border-radius: 3px;
            transition: all 0.15s ease;
            cursor: pointer;
        }

        /* 🎨 简洁现代按钮样式 */
        .copy-button {
            background: var(--button-success) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .copy-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .delete-button {
            background: var(--button-danger) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .delete-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .edit-button {
            background: var(--button-warning) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .edit-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .pin-button {
            background: var(--button-info) !important;
            color: white !important;
            width: 40px;
            height: 40px;
            padding: 0 !important;
            min-height: 40px;
            border: none !important;
        }

        .pin-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .toggle-expand-button {
            background: var(--button-secondary) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .toggle-expand-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .add-button {
            background: var(--button-primary) !important;
            color: white !important;
            border: none !important;
        }

        .add-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .add-section-btn {
            background: var(--button-success) !important;
            color: white !important;
            border: none !important;
        }

        .add-section-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        /* 功能面板内部布局修复 */
        .collapsible-content {
            overflow: hidden;
            transition: max-height 0.3s ease, opacity 0.3s ease;
        }

        .collapsible-content.collapsed {
            max-height: 0 !important;
            opacity: 0;
            margin: 0;
        }

        .collapsible-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .collapsible-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        .collapsible-toggle {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            transition: transform 0.3s ease;
            padding: 4px;
            border-radius: 4px;
        }

        .collapsible-toggle:hover {
            background: rgba(0, 123, 255, 0.1);
        }

        .collapsible-toggle.collapsed {
            transform: rotate(-90deg);
        }

        /* 分区管理样式 */
        .section-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--card-border);
        }

        .add-section-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .add-section-input {
            flex-grow: 1;
        }

        /* 批量管理样式 */
        .batch-management {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--card-border);
        }

        .batch-management h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .batch-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        #batchActions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        #batchCounter {
            color: var(--text-medium);
            font-size: 12px;
            white-space: nowrap;
        }

        /* 搜索容器样式 */
        .search-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-container input {
            flex-grow: 1;
        }

        /* 排序选项样式 */
        .sort-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .expand-control-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }

        .compact-tips {
            margin: 10px 0;
            padding: 8px 12px;
            background: rgba(74, 144, 226, 0.08);
            border-radius: 8px;
            font-size: 12px;
            color: var(--text-medium);
            line-height: 1.4;
        }

        /* 🌟 简洁置顶样式 */
        .snippet.pinned {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(16, 185, 129, 0.1)) !important;
            border: 2px solid rgba(59, 130, 246, 0.4) !important;
            position: relative;
        }

        /* 🌟 简洁置顶按钮状态 */
        .pin-button.pinned {
            background: var(--bg-secondary) !important;
            color: white !important;
            box-shadow: var(--shadow-glow) !important;
        }

        .pin-button.pinned:hover {
            transform: translateY(-1px);
        }

        /* 🎪 增强动画效果 */
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.02); opacity: 0.95; }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-30px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(30px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -8px, 0);
            }
            70% {
                transform: translate3d(0, -4px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
        }

        /* 🌟 增强页面加载动画 */
        .snippet {
            animation: slideInUp 0.4s ease-out;
        }

        .snippet:nth-child(even) {
            animation: slideInLeft 0.4s ease-out;
        }

        .snippet:nth-child(odd) {
            animation: slideInRight 0.4s ease-out;
        }

        .header {
            animation: fadeIn 0.8s ease-out;
        }

        .section-tab {
            animation: fadeInScale 0.3s ease-out;
        }

        .section-tab:nth-child(2) { animation-delay: 0.1s; }
        .section-tab:nth-child(3) { animation-delay: 0.2s; }
        .section-tab:nth-child(4) { animation-delay: 0.3s; }
        .section-tab:nth-child(5) { animation-delay: 0.4s; }

        /* 成功消息动画 */
        .success-message {
            animation: bounce 0.6s ease-out;
        }

        /* 错误动画 */
        .error-shake {
            animation: shake 0.5s ease-in-out;
        }

        /* 按钮点击动画 */
        .button-clicked {
            animation: bounce 0.3s ease-out;
        }

        /* 搜索高亮动画 */
        .search-highlight {
            animation: glow 1s ease-in-out infinite;
        }

        /* 🎨 主题系统 */
        .theme-sunset {
            --bg-primary: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
            --bg-secondary: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --bg-tertiary: linear-gradient(135deg, #fb923c 0%, #ea580c 100%);
            --bg-quaternary: linear-gradient(135deg, #f97316 0%, #c2410c 100%);
            --card-bg: rgba(254, 243, 199, 0.9);
            --text-color: #92400e;
            --text-medium: #a16207;
            --text-dim: #ca8a04;
            --border-color: rgba(245, 158, 11, 0.3);
            --card-border: rgba(217, 119, 6, 0.2);
            --bg-glass: rgba(254, 243, 199, 0.8);
            --bg-glass-hover: rgba(254, 243, 199, 0.9);
            --highlight-color: #f59e0b;
        }

        .theme-forest {
            --bg-primary: linear-gradient(135deg, #d1fae5 0%, #10b981 100%);
            --bg-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --bg-tertiary: linear-gradient(135deg, #34d399 0%, #10b981 100%);
            --bg-quaternary: linear-gradient(135deg, #6ee7b7 0%, #34d399 100%);
            --card-bg: rgba(209, 250, 229, 0.9);
            --text-color: #065f46;
            --text-medium: #047857;
            --text-dim: #059669;
            --border-color: rgba(16, 185, 129, 0.3);
            --card-border: rgba(5, 150, 105, 0.2);
            --bg-glass: rgba(209, 250, 229, 0.8);
            --bg-glass-hover: rgba(209, 250, 229, 0.9);
            --highlight-color: #10b981;
        }

        .theme-ocean {
            --bg-primary: linear-gradient(135deg, #dbeafe 0%, #3b82f6 100%);
            --bg-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            --bg-tertiary: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            --bg-quaternary: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
            --card-bg: rgba(219, 234, 254, 0.9);
            --text-color: #1e40af;
            --text-medium: #1d4ed8;
            --text-dim: #2563eb;
            --border-color: rgba(59, 130, 246, 0.3);
            --card-border: rgba(29, 78, 216, 0.2);
            --bg-glass: rgba(219, 234, 254, 0.8);
            --bg-glass-hover: rgba(219, 234, 254, 0.9);
            --highlight-color: #3b82f6;
        }

        .theme-purple {
            --bg-primary: linear-gradient(135deg, #ede9fe 0%, #8b5cf6 100%);
            --bg-secondary: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            --bg-tertiary: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
            --bg-quaternary: linear-gradient(135deg, #c4b5fd 0%, #a78bfa 100%);
            --card-bg: rgba(237, 233, 254, 0.9);
            --text-color: #5b21b6;
            --text-medium: #7c3aed;
            --text-dim: #8b5cf6;
            --border-color: rgba(139, 92, 246, 0.3);
            --card-border: rgba(124, 58, 237, 0.2);
            --bg-glass: rgba(237, 233, 254, 0.8);
            --bg-glass-hover: rgba(237, 233, 254, 0.9);
            --highlight-color: #8b5cf6;
        }

        .theme-rose {
            --bg-primary: linear-gradient(135deg, #fce7f3 0%, #ec4899 100%);
            --bg-secondary: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
            --bg-tertiary: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
            --bg-quaternary: linear-gradient(135deg, #f9a8d4 0%, #f472b6 100%);
            --card-bg: rgba(252, 231, 243, 0.9);
            --text-color: #be185d;
            --text-medium: #db2777;
            --text-dim: #ec4899;
            --border-color: rgba(236, 72, 153, 0.3);
            --card-border: rgba(219, 39, 119, 0.2);
            --bg-glass: rgba(252, 231, 243, 0.8);
            --bg-glass-hover: rgba(252, 231, 243, 0.9);
            --highlight-color: #ec4899;
        }

        /* 🌙 深色模式 */
        .dark-mode {
            --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            --bg-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            --bg-tertiary: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --bg-quaternary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --card-bg: rgba(30, 41, 59, 0.9);
            --text-color: #f1f5f9;
            --text-medium: #cbd5e1;
            --text-dim: #94a3b8;
            --border-color: rgba(59, 130, 246, 0.3);
            --card-border: rgba(148, 163, 184, 0.2);
            --bg-glass: rgba(30, 41, 59, 0.8);
            --bg-glass-hover: rgba(30, 41, 59, 0.9);
            --highlight-color: #3b82f6;
            --gradient-text: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        /* 主题选择器样式 */
        .theme-option:hover {
            border-color: var(--highlight-color) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-option.active {
            border-color: var(--highlight-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .dark-mode body {
            background: var(--bg-primary);
        }

        .dark-mode body::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.08) 0%, transparent 50%);
        }

        .dark-mode .snippet {
            background: var(--card-bg);
            border-color: var(--card-border);
            color: var(--text-color);
        }

        .dark-mode .snippet:hover {
            border-color: var(--highlight-color);
            box-shadow: var(--shadow-hover);
            background: var(--bg-glass-hover);
        }

        .dark-mode h1 {
            color: var(--text-color);
        }

        /* 📱 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            h1 {
                font-size: 2rem;
            }

            .snippet {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .actions {
                justify-content: center;
                flex-wrap: wrap;
            }

            .header div {
                flex-wrap: wrap;
                gap: 10px;
            }

            .sort-options, .batch-controls {
                justify-content: center;
            }

            textarea {
                width: 100%;
                min-height: 120px;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 1.8rem;
            }

            .snippet {
                padding: 15px;
            }

            .copy-button, .delete-button, .edit-button, .pin-button {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* ⌨️ 键盘快捷键提示 */
        .keyboard-hint {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--card-bg);
            border: var(--glass-border);
            border-radius: var(--border-radius);
            padding: 10px 15px;
            font-size: 12px;
            color: var(--text-medium);
            box-shadow: var(--shadow);
            backdrop-filter: var(--backdrop-blur);
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
            z-index: 1000;
        }

        .keyboard-hint.show {
            opacity: 1;
            transform: translateY(0);
        }

        .keyboard-hint kbd {
            background: var(--highlight-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <!-- 🌟 清晰易读的头部区域 -->
    <div class="header">
        <h1>🌟 超级美化网络剪切板</h1>
        <p style="text-align: center; margin-top: 15px; font-size: 1.1rem; color: var(--text-medium); font-weight: 500;">
            ✨ 云端智能管理 · 让数据更有序 ✨
        </p>
        <div style="display: flex; gap: 15px; margin-top: 25px; justify-content: center; flex-wrap: wrap;">
            <button id="darkModeToggle" class="dark-mode-toggle" onclick="toggleDarkMode()" title="切换深色模式" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-size: 22px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span id="darkModeIcon">🌙</span>
            </button>
            <button id="themeToggle" class="dark-mode-toggle" onclick="showThemeSelector()" title="主题选择" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-size: 22px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>🎨</span>
            </button>
            <button id="dataRecoveryBtn" class="dark-mode-toggle" onclick="showDataRecovery()" title="数据恢复" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-size: 22px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>🔧</span>
            </button>
            <button class="dark-mode-toggle" onclick="showStats()" title="统计信息" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 50px; height: 50px; cursor: pointer; font-size: 22px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>📊</span>
            </button>
        </div>
    </div>

    <!-- 🎨 主题选择面板 -->
    <div id="themePanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600;">🎨 主题选择</h3>
        <p style="color: var(--text-medium); margin-bottom: 20px; line-height: 1.5;">选择您喜欢的颜色主题：</p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div class="theme-option" data-theme="default" onclick="applyTheme('default')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                <div style="color: #1e293b; font-weight: 600; margin-bottom: 5px;">🌟 默认</div>
                <div style="font-size: 12px; color: #64748b;">清爽蓝色</div>
            </div>
            <div class="theme-option" data-theme="sunset" onclick="applyTheme('sunset')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);">
                <div style="color: #92400e; font-weight: 600; margin-bottom: 5px;">🌅 日落</div>
                <div style="font-size: 12px; color: #a16207;">温暖橙色</div>
            </div>
            <div class="theme-option" data-theme="forest" onclick="applyTheme('forest')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #d1fae5 0%, #10b981 100%);">
                <div style="color: #065f46; font-weight: 600; margin-bottom: 5px;">🌲 森林</div>
                <div style="font-size: 12px; color: #047857;">自然绿色</div>
            </div>
            <div class="theme-option" data-theme="ocean" onclick="applyTheme('ocean')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #dbeafe 0%, #3b82f6 100%);">
                <div style="color: #1e40af; font-weight: 600; margin-bottom: 5px;">🌊 海洋</div>
                <div style="font-size: 12px; color: #1d4ed8;">深邃蓝色</div>
            </div>
            <div class="theme-option" data-theme="purple" onclick="applyTheme('purple')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #ede9fe 0%, #8b5cf6 100%);">
                <div style="color: #5b21b6; font-weight: 600; margin-bottom: 5px;">💜 紫罗兰</div>
                <div style="font-size: 12px; color: #7c3aed;">优雅紫色</div>
            </div>
            <div class="theme-option" data-theme="rose" onclick="applyTheme('rose')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #fce7f3 0%, #ec4899 100%);">
                <div style="color: #be185d; font-weight: 600; margin-bottom: 5px;">🌹 玫瑰</div>
                <div style="font-size: 12px; color: #db2777;">浪漫粉色</div>
            </div>
        </div>
        <button onclick="hideThemeSelector()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
    </div>

    <!-- 简洁数据恢复面板 -->
    <div id="dataRecoveryPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600;">🔧 数据恢复工具</h3>
        <p style="color: var(--text-medium); margin-bottom: 15px; line-height: 1.5;">如果您的数据丢失或显示异常，请尝试以下恢复选项：</p>
        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-bottom: 15px;">
            <button onclick="checkLocalStorageData()" style="padding: 10px 16px; background: var(--button-info); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">检查本地数据</button>
            <button onclick="restoreFromBackup()" style="padding: 10px 16px; background: var(--button-success); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">从备份恢复</button>
            <button onclick="showRawData()" style="padding: 10px 16px; background: var(--button-warning); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">查看原始数据</button>
            <button onclick="clearAndReset()" style="padding: 10px 16px; background: var(--button-danger); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">清空重置</button>
        </div>
        <button onclick="hideDataRecovery()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
    </div>

    <!-- 💡 简洁智能提示 -->
    <div style="background: var(--bg-glass); backdrop-filter: var(--backdrop-blur); border: var(--glass-border); border-radius: var(--border-radius); padding: 15px; margin: 20px auto; width: 90%; text-align: center; box-shadow: var(--shadow);">
        <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
            <span style="font-size: 1.5em; animation: pulse 2s infinite;">💡</span>
            <span style="color: var(--text-color); font-weight: 600;">智能提示：点击内容可智能选择复制片段</span>
        </div>
    </div>

    <!-- 🛠️ 简洁功能面板 -->
    <div style="background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); margin: 20px auto; width: 96%; box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); overflow: hidden;">
        <div style="background: var(--bg-secondary); padding: 20px; cursor: pointer; transition: var(--transition);" onclick="toggleCollapsibleSection('toolbox')">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <h3 style="color: var(--text-light); font-size: 1.3rem; margin: 0; display: flex; align-items: center; gap: 10px; font-weight: 600;">
                    <span style="font-size: 1.4em;">🛠️</span>
                    功能面板
                </h3>
                <button style="background: rgba(255,255,255,0.2); border: none; color: var(--text-light); font-size: 1.2em; cursor: pointer; transition: var(--transition); border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;" id="toolboxToggle">🔼</button>
            </div>
        </div>
        <div class="collapsible-content collapsed" id="toolboxContent">
            <!-- 分区管理 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--card-border);">
                <h4 style="margin: 0 0 10px 0; display: flex; align-items: center; gap: 6px; font-size: 14px;">📂 分区管理</h4>
                <div class="section-tabs" id="sectionTabs">
                    <div class="section-tab active" data-section="all" onclick="switchSection('all')">
                        <span>全部</span>
                        <span class="section-count" id="allCount">0</span>
                    </div>
                </div>
                
                <div class="add-section-container">
                    <input type="text" class="add-section-input" id="newSectionName" placeholder="输入新分区名称..." onkeydown="handleSectionInput(event)">
                    <button class="add-section-btn" onclick="addNewSection()">➕ 添加</button>
                </div>
                
                <!-- 批量管理 -->
                <div class="batch-management">
                    <div class="batch-controls">
                        <button onclick="toggleBatchMode()" id="batchModeBtn">开启批量</button>
                        <div id="batchActions" style="display: none; gap: 8px; align-items: center;">
                            <select id="batchTargetSection" class="add-section-input" style="width: auto; padding: 5px;">
                                <option value="">选择目标分区</option>
                            </select>
                            <button onclick="batchMoveToSection()" style="background: var(--button-success); color: white;">移动</button>
                            <button onclick="batchDelete()" style="background: var(--button-danger); color: white;">删除</button>
                        </div>
                        <span id="batchCounter">已选择 0 项</span>
                    </div>
                </div>
            </div>
            
            <!-- 搜索功能 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--card-border);">
                <h4 style="margin: 0 0 10px 0; display: flex; align-items: center; gap: 6px; font-size: 14px;">🔍 搜索功能</h4>
                <div class="search-container" style="margin: 0; width: 100%;">
                    <input type="text" id="searchInput" placeholder="🔍 搜索内容、标签、分区..."">
                    <button onclick="clearSearch()">清除</button>
                </div>
            </div>
            
            <!-- 排序和控制 -->
            <div>
                <h4 style="margin: 0 0 10px 0; display: flex; align-items: center; gap: 6px; font-size: 14px;">⚙️ 排序和控制</h4>
                <div class="sort-options" style="margin: 0 0 15px 0; width: 100%;">
                    <button onclick="sortSnippets('original')">原始顺序</button>
                    <button onclick="sortSnippets('date-asc')">按时间升序</button>
                    <button onclick="sortSnippets('date-desc')">按时间降序</button>
                    <button onclick="sortSnippets('text-asc')">按文本升序</button>
                    <button onclick="sortSnippets('text-desc')">按文本降序</button>
                </div>
                
                <div class="expand-control-container" style="margin: 0 0 10px 0;">
                    <button id="toggleAllExpandBtn" onclick="toggleAllExpand()" class="toggle-all-expand-btn">
                        <span id="toggleAllIcon">📤</span> 全部折叠
                    </button>
                </div>
                
                <div class="compact-tips">
                    <strong>💡 快速提示：</strong>
                    点击内容可智能选择复制片段 • 可在显示控制中开启分区信息和快速分区按钮
                </div>
            </div>

            <!-- 显示控制 -->
            <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid var(--card-border);">
                <h4 style="margin: 0 0 8px 0; font-size: 14px; display: flex; align-items: center; gap: 6px;">👁️ 显示控制</h4>
                <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 12px; cursor: pointer;">
                        <input type="checkbox" id="showSectionInfoCheck" onchange="toggleSectionInfo()" style="cursor: pointer;">
                        显示分区信息
                    </label>
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 12px; cursor: pointer;">
                        <input type="checkbox" id="showQuickSectionsCheck" onchange="toggleQuickSections()" style="cursor: pointer;">
                        显示快速分区
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加内容 -->
    <div class="collapsible-section">
        <div class="collapsible-header" onclick="toggleCollapsibleSection('addContent')">
            <h3>➕ 添加内容</h3>
            <button class="collapsible-toggle" id="addContentToggle">🔽</button>
        </div>
        <div class="collapsible-content" id="addContentContent">
            <div class="add-area" style="margin: 0; width: 100%;">
                <button class="advanced-toggle-btn" onclick="toggleAdvancedFeatures('top')">⚙️ 高级功能</button>
                <textarea id="newSnippetTop" placeholder="输入新的剪贴板内容"></textarea>
                
                <div class="advanced-features" id="advanced-features-top">
                    <div class="upload-container">
                        <input type="file" id="fileUploadTop" style="display: none;">
                        <button onclick="document.getElementById('fileUploadTop').click()">📄 选择文件</button>
                        <span id="fileNameTop"></span>
                        <input type="file" id="imageUploadTop" accept="image/*" style="display: none;">
                        <button onclick="document.getElementById('imageUploadTop').click()">🖼️ 选择图片</button>
                    </div>
                    <img id="imagePreviewTop" alt="图片预览" style="display:none;">
                    
                    <div class="tags-input-container">
                        <div id="tags-display-top" class="tags-display"></div>
                        <input type="text" class="tag-input" placeholder="添加标签 (回车确认)" onkeydown="handleTagInput(event, 'top')">
                    </div>
                    
                    <!-- 分区选择 -->
                    <div class="section-selector-container">
                        <label>选择分区：</label>
                        <select id="sectionSelect-top">
                            <option value="">默认</option>
                        </select>
                    </div>
                </div>
                <button class="add-button" style="width: 100%;" onclick="addSnippet('top')">添加</button>
            </div>
        </div>
    </div>

    <div id="snippets"></div>

    <div class="export-import-container" style="background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 20px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.1rem; font-weight: 600; display: flex; align-items: center; justify-content: center; gap: 8px;">
            <span style="font-size: 1.3em;">📁</span>
            数据管理
        </h3>
        <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
            <button onclick="exportData()" class="export-button" style="padding: 12px 20px; background: var(--button-success); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; font-size: 14px; transition: var(--transition); box-shadow: var(--shadow);">
                📤 导出数据
            </button>
            <button onclick="document.getElementById('importFile').click()" class="import-button" style="padding: 12px 20px; background: var(--button-info); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; font-size: 14px; transition: var(--transition); box-shadow: var(--shadow);">
                📥 导入数据
            </button>
        </div>
        <input type="file" id="importFile" style="display: none;" accept=".json" onchange="importData(event)">
    </div>

    <!-- ⌨️ 键盘快捷键提示 -->
    <div id="keyboardHint" class="keyboard-hint">
        <div style="font-weight: bold; margin-bottom: 8px; color: var(--highlight-color);">⌨️ 键盘快捷键</div>
        <div><kbd>Ctrl</kbd> + <kbd>Enter</kbd> 快速添加</div>
        <div><kbd>Ctrl</kbd> + <kbd>F</kbd> 搜索内容</div>
        <div><kbd>Ctrl</kbd> + <kbd>D</kbd> 切换深色模式</div>
        <div><kbd>Ctrl</kbd> + <kbd>Z</kbd> 撤销操作</div>
        <div><kbd>Ctrl</kbd> + <kbd>Y</kbd> 重做操作</div>
        <div><kbd>Ctrl</kbd> + <kbd>A</kbd> 全选 (批量模式)</div>
        <div><kbd>Ctrl</kbd> + <kbd>B</kbd> 切换批量模式</div>
        <div><kbd>Ctrl</kbd> + <kbd>S</kbd> 导出数据</div>
        <div><kbd>Delete</kbd> 删除选中项</div>
        <div><kbd>Escape</kbd> 退出/清除</div>
        <div><kbd>F1</kbd> 显示/隐藏此提示</div>
    </div>

    <script>
        // 全局变量
        let currentImageBase64 = '';
        let currentFileData = null;
        let editingIndex = -1;
        let currentSort = 'original';
        let currentSearchTerm = '';
        let currentTags = [];
        let allTags = new Set();
        let currentTagFilter = 'all';
        let isAllExpanded = true;
        let currentSection = 'all';
        let allSections = new Set(['默认']);

        // 批量管理相关变量
        let batchMode = false;
        let selectedIndices = new Set();

        // 显示控制变量
        let showSectionInfo = false;  // 默认隐藏分区信息
        let showQuickSections = false;  // 默认隐藏快速分区

        // 拖拽相关变量
        let draggedElement = null;
        let draggedIndex = -1;
        let dropIndicator = null;

        // 撤销重做相关变量
        let undoStack = [];
        let redoStack = [];
        const MAX_UNDO_STEPS = 20;

        // 获取DOM元素
        const snippetsDiv = document.getElementById('snippets');
        const newSnippetTextareaTop = document.getElementById('newSnippetTop');
        const imageUploadTop = document.getElementById('imageUploadTop');
        const fileUploadTop = document.getElementById('fileUploadTop');
        const imagePreviewTop = document.getElementById('imagePreviewTop');
        const fileNameTop = document.getElementById('fileNameTop');

        // 初始化
        window.addEventListener('DOMContentLoaded', function() {
            // 先加载分区信息
            loadSections();
            
            // 加载保存的排序状态
            const savedSort = localStorage.getItem('currentSort');
            if (savedSort) {
                currentSort = savedSort;
            }
            
            loadSnippets();
            updateSectionTabs();
            updateSectionSelects();
            
            // 初始化折叠状态
            initCollapsibleSections();
            
            // 初始化显示设置
            initDisplaySettings();
            
            // 设置事件监听器
            setupInputHandlers('top');
            
            // 深色模式
            const savedDarkMode = localStorage.getItem('darkMode');
            if (savedDarkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                document.getElementById('darkModeIcon').textContent = '☀️';
            }

            // 初始化键盘快捷键
            initKeyboardShortcuts();

            // 显示键盘快捷键提示（3秒后自动隐藏）
            setTimeout(() => {
                showKeyboardHint();
                setTimeout(hideKeyboardHint, 3000);
            }, 1000);
        });

        // 加载保存的分区信息
        function loadSections() {
            try {
                const savedSections = localStorage.getItem('sections');
                if (savedSections) {
                    allSections = new Set(JSON.parse(savedSections));
                } else {
                    allSections = new Set(['默认']);
                }
            } catch (error) {
                console.error('加载分区信息失败:', error);
                allSections = new Set(['默认']);
            }
        }

        // 保存分区信息
        function saveSections() {
            try {
                localStorage.setItem('sections', JSON.stringify([...allSections]));
                return true;
            } catch (error) {
                console.error('保存分区信息失败:', error);
                return false;
            }
        }

        // 设置输入处理器
        function setupInputHandlers(position) {
            const textarea = newSnippetTextareaTop;
            const imageUpload = imageUploadTop;
            const fileUpload = fileUploadTop;

            textarea.addEventListener('keydown', function(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    addSnippet(position);
                }
            });

            imageUpload.addEventListener('change', function(event) {
                handleImageUpload(position, event);
            });

            fileUpload.addEventListener('change', function(event) {
                handleFileUpload(position, event);
            });
        }

        // 处理图片上传
        function handleImageUpload(position, event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 5 * 1024 * 1024) {
                    alert('图片大小不能超过5MB');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreviewTop.src = e.target.result;
                    imagePreviewTop.style.display = "block";
                    currentImageBase64 = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        }

        // 处理文件上传
        function handleFileUpload(position, event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentFileData = {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        data: e.target.result
                    };
                    fileNameTop.textContent = file.name;
                }
                reader.readAsDataURL(file);
            }
        }

        // 获取snippets数据
        function getSnippets() {
            try {
                let snippets = JSON.parse(localStorage.getItem('snippets') || '[]');
                
                // 完全保持原有数据格式，只在必要时添加缺失字段
                return snippets.map((snippet, originalIndex) => {
                    if (typeof snippet === 'string') {
                        // 如果是旧格式的字符串，转换为对象但保留原有内容和顺序
                        return { 
                            content: snippet, 
                            timestamp: Date.now() - (snippets.length - originalIndex) * 60000, // 使用分钟间隔保持顺序
                            index: originalIndex,
                            originalIndex: originalIndex, // 保存原始位置
                            pinned: false,
                            section: '默认',
                            tags: []
                        };
                    } else {
                        // 如果已经是对象，完全保留原有数据，只补充缺失的字段
                        return {
                            content: snippet.content || '',
                            // 优先使用原有时间戳，如果没有则根据原始位置生成
                            timestamp: snippet.timestamp || (Date.now() - (snippets.length - originalIndex) * 60000),
                            index: snippet.index !== undefined ? snippet.index : originalIndex,
                            originalIndex: snippet.originalIndex !== undefined ? snippet.originalIndex : originalIndex,
                            pinned: snippet.pinned || false,
                            section: snippet.section || '默认',
                            tags: snippet.tags || [],
                            // 保留所有其他可能存在的字段
                            ...snippet
                        };
                    }
                });
            } catch (error) {
                console.error('读取数据出错:', error);
                // 尝试从备份恢复
                try {
                    const backup = localStorage.getItem('snippets_backup_auto');
                    if (backup) {
                        console.log('尝试从自动备份恢复数据');
                        const backupData = JSON.parse(backup);
                        return backupData.map((snippet, originalIndex) => {
                            if (typeof snippet === 'string') {
                                return { 
                                    content: snippet, 
                                    timestamp: Date.now() - (backupData.length - originalIndex) * 60000,
                                    index: originalIndex,
                                    originalIndex: originalIndex,
                                    pinned: false,
                                    section: '默认',
                                    tags: []
                                };
                            }
                            return { ...snippet, originalIndex: originalIndex };
                        });
                    }
                } catch (e) {
                    console.error('备份恢复也失败:', e);
                }
                return [];
            }
        }

        // 保存snippets数据
        function saveSnippets(snippets) {
            try {
                // 在保存新数据前，先备份当前数据
                const currentData = localStorage.getItem('snippets');
                if (currentData) {
                    localStorage.setItem('snippets_backup_auto', currentData);
                }
                
                localStorage.setItem('snippets', JSON.stringify(snippets));
                return true;
            } catch (error) {
                console.error('保存数据出错:', error);
                alert('保存失败: ' + error.message);
                return false;
            }
        }

        // 加载snippets
        function loadSnippets() {
            const snippets = getSnippets();

            // 提取所有标签和分区
            allTags = new Set();
            const foundSections = new Set(['默认']);
            snippets.forEach(snippet => {
                if (snippet.tags && Array.isArray(snippet.tags)) {
                    snippet.tags.forEach(tag => allTags.add(tag));
                }
                if (snippet.section) {
                    foundSections.add(snippet.section);
                }
            });
            
            // 合并已有分区和从数据中发现的分区
            foundSections.forEach(section => allSections.add(section));
            
            // 保存分区信息
            saveSections();

            // 过滤分区
            let filteredSnippets = snippets;
            if (currentSection !== 'all') {
                filteredSnippets = snippets.filter(snippet => 
                    (snippet.section || '默认') === currentSection
                );
            }

            // 增强搜索过滤 - 支持内容、标签、分区搜索
            if (currentSearchTerm) {
                filteredSnippets = filteredSnippets.filter(snippet =>
                    matchesSearch(snippet, currentSearchTerm)
                );
            }

            // 排序
            const sortedSnippets = sortSnippetsData(filteredSnippets, currentSort);

            // 显示
            displaySnippets(sortedSnippets);
            updateSectionTabs();
            updateSectionSelects();
        }

        // 显示snippets
        function displaySnippets(snippets) {
            snippetsDiv.innerHTML = '';
            
            if (snippets.length === 0) {
                snippetsDiv.innerHTML = '<p style="text-align: center; color: #888;">暂无数据</p>';
                return;
            }

            snippets.forEach(snippetData => {
                const snippetDiv = document.createElement('div');
                snippetDiv.classList.add('snippet');
                snippetDiv.dataset.index = snippetData.index;

                // 添加拖拽属性
                snippetDiv.draggable = true;

                if (snippetData.pinned) {
                    snippetDiv.classList.add('pinned');
                }

                // 批量模式下的选择效果
                if (batchMode && selectedIndices.has(snippetData.index)) {
                    snippetDiv.style.background = 'rgba(0, 123, 255, 0.1)';
                    snippetDiv.style.border = '2px solid var(--highlight-color)';
                }

                // 添加拖拽事件监听器
                setupDragEvents(snippetDiv, snippetData.index);

                // 添加拖拽手柄
                const dragHandle = document.createElement('div');
                dragHandle.classList.add('drag-handle');
                dragHandle.innerHTML = '⋮⋮';
                dragHandle.title = '拖拽排序';

                const snippetContent = document.createElement('div');
                snippetContent.classList.add('snippet-content');
                snippetContent.innerHTML = processUrls(snippetData.content);

                // 批量模式下添加复选框
                if (batchMode) {
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.checked = selectedIndices.has(snippetData.index);
                    checkbox.style.cssText = 'margin-right: 10px; cursor: pointer;';
                    checkbox.addEventListener('change', () => {
                        toggleSnippetSelection(snippetData.index);
                    });
                    
                    snippetContent.insertBefore(checkbox, snippetContent.firstChild);
                    
                    // 点击整个content区域也能选择
                    snippetContent.addEventListener('click', function(event) {
                        if (event.target === checkbox) return; // 避免双重触发
                        toggleSnippetSelection(snippetData.index);
                        checkbox.checked = selectedIndices.has(snippetData.index);
                    });
                } else {
                    // 非批量模式下的事件监听
                    snippetContent.addEventListener('click', function(event) {
                        // 检查点击的是否是链接或其他交互元素
                        if (event.target.tagName === 'A' || event.target.closest('a')) {
                            return; // 让链接正常工作
                        }
                        event.preventDefault();
                        event.stopPropagation();
                        performSmartCopy(event, snippetData.content);
                    });

                    // 鼠标移动时高亮当前片段
                    snippetContent.addEventListener('mousemove', function(event) {
                        highlightHoverSegment(event, snippetData.content, this);
                    });

                    // 鼠标离开时清除高亮
                    snippetContent.addEventListener('mouseleave', function() {
                        clearHoverHighlight(this);
                    });
                }

                const actions = document.createElement('div');
                actions.classList.add('actions');

                // 创建按钮
                const buttons = [
                    {
                        text: snippetData.pinned ? '🔓' : '📌',
                        class: snippetData.pinned ? 'pin-button pinned' : 'pin-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            togglePin(snippetData.index); 
                        }
                    },
                    {
                        text: isAllExpanded ? '折叠 ▲' : '展开 ▼',
                        class: 'toggle-expand-button',
                        handler: function(e) {
                            e.stopPropagation();
                            toggleExpand(e.target, snippetContent);
                        }
                    },
                    {
                        text: '编辑',
                        class: 'edit-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            editSnippet(snippetData.index); 
                        }
                    },
                    {
                        text: '复制',
                        class: 'copy-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            copySnippet(snippetData.index); 
                        }
                    },
                    {
                        text: '删除',
                        class: 'delete-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            deleteSnippet(snippetData.index); 
                        }
                    }
                ];

                buttons.forEach(btn => {
                    const button = document.createElement('button');
                    button.innerHTML = btn.text;
                    button.className = btn.class;
                    button.onclick = function(e) {
                        addButtonClickAnimation(button);
                        btn.handler(e);
                    };
                    actions.appendChild(button);
                });

                // 添加分区选择下拉菜单
                const sectionSelectContainer = document.createElement('div');
                sectionSelectContainer.style.cssText = 'margin-left: 8px;';
                
                const sectionSelect = document.createElement('select');
                sectionSelect.className = 'section-selector';
                sectionSelect.value = snippetData.section || '默认';
                
                // 添加所有分区选项
                allSections.forEach(section => {
                    const option = document.createElement('option');
                    option.value = section;
                    option.textContent = section;
                    if (section === (snippetData.section || '默认')) {
                        option.selected = true;
                    }
                    sectionSelect.appendChild(option);
                });

                sectionSelect.addEventListener('change', function(e) {
                    e.stopPropagation();
                    moveToSection(snippetData.index, this.value);
                });

                sectionSelectContainer.appendChild(sectionSelect);
                actions.appendChild(sectionSelectContainer);

                // 显示标签
                if (snippetData.tags && snippetData.tags.length > 0) {
                    const tagsContainer = document.createElement('div');
                    snippetData.tags.forEach(tag => {
                        const tagEl = document.createElement('span');
                        tagEl.classList.add('tag');
                        tagEl.textContent = tag;
                        tagsContainer.appendChild(tagEl);
                    });
                    snippetContent.appendChild(tagsContainer);
                }

                // 显示分区信息（可控制显示）
                if (showSectionInfo && snippetData.section && snippetData.section !== '默认') {
                    const sectionEl = document.createElement('div');
                    sectionEl.textContent = `分区: ${snippetData.section}`;
                    sectionEl.style.fontSize = '12px';
                    sectionEl.style.color = 'var(--text-medium)';
                    sectionEl.style.marginTop = '5px';
                    snippetContent.appendChild(sectionEl);
                }

                // 在全部视图中显示快速分区操作（可控制显示）
                if (showQuickSections && currentSection === 'all') {
                    const quickSectionDiv = document.createElement('div');
                    quickSectionDiv.style.cssText = 'margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee;';
                    
                    const quickLabel = document.createElement('span');
                    quickLabel.textContent = '快速分区: ';
                    quickLabel.style.cssText = 'font-size: 12px; color: var(--text-medium); margin-right: 8px;';
                    
                    quickSectionDiv.appendChild(quickLabel);
                    
                    // 为每个分区创建快速按钮
                    allSections.forEach(section => {
                        if (section !== (snippetData.section || '默认')) {
                            const quickBtn = document.createElement('button');
                            quickBtn.textContent = section;
                            quickBtn.style.cssText = 'font-size: 11px; padding: 2px 6px; margin: 0 3px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;';
                            quickBtn.onclick = (e) => {
                                e.stopPropagation();
                                moveToSection(snippetData.index, section);
                            };
                            quickBtn.onmouseover = () => {
                                quickBtn.style.background = 'var(--highlight-color)';
                                quickBtn.style.color = 'white';
                            };
                            quickBtn.onmouseout = () => {
                                quickBtn.style.background = '#f0f0f0';
                                quickBtn.style.color = 'black';
                            };
                            quickSectionDiv.appendChild(quickBtn);
                        }
                    });
                    
                    snippetContent.appendChild(quickSectionDiv);
                }

                snippetDiv.appendChild(dragHandle);
                snippetDiv.appendChild(snippetContent);
                snippetDiv.appendChild(actions);
                snippetsDiv.appendChild(snippetDiv);
            });
        }

        // 处理URL
        function processUrls(text) {
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            return text.replace(urlRegex, '<a href="$1" target="_blank">$1</a>');
        }

        // 添加snippet
        function addSnippet(position) {
            let snippet = newSnippetTextareaTop.value.trim();
            let image = currentImageBase64;
            let fileData = currentFileData;
            let tags = [...currentTags];
            let section = document.getElementById('sectionSelect-top').value || '默认';

            if (snippet === '' && image === '' && !fileData) return;

            let content = '';
            if (image !== '') {
                content += `<img src="${image}" style="max-width: 100px; max-height: 100px;"><br>`;
            }
            if (fileData) {
                content += `<div class="file-attachment" data-file='${JSON.stringify(fileData)}'>
                    📎 <a href="#" onclick="downloadFile(this.parentElement); event.preventDefault();">${fileData.name}</a>
                </div><br>`;
            }
            content += snippet;

            const newSnippet = {
                content,
                timestamp: new Date().getTime(),
                index: getSnippets().length,
                tags: tags,
                pinned: false,
                section: section
            };

            // 清空输入
            newSnippetTextareaTop.value = '';
            imagePreviewTop.style.display = "none";
            fileNameTop.textContent = '';
            currentImageBase64 = '';
            currentFileData = null;
            currentTags = [];

            const snippets = getSnippets();

            // 保存撤销状态
            saveStateForUndo('add', { snippet: newSnippet });

            snippets.push(newSnippet);

            if (saveSnippets(snippets)) {
                showSuccessMessage('添加成功！');
                loadSnippets();
            }
        }

        // 复制snippet
        function copySnippet(index) {
            const snippets = getSnippets();
            const snippetData = snippets.find(s => s.index === index);
            if (snippetData) {
                const textToCopy = snippetData.content.replace(/<[^>]+>/g, '');
                navigator.clipboard.writeText(textToCopy)
                    .then(() => showSuccessMessage('复制成功！'))
                    .catch(() => alert('复制失败'));
            }
        }

        // 删除snippet (支持撤销)
        function deleteSnippet(index) {
            const snippets = getSnippets();
            const snippetToDelete = snippets.find(s => s.index === index);

            if (snippetToDelete && confirm('确定要删除这条记录吗？')) {
                // 保存撤销状态
                saveStateForUndo('delete', { snippet: snippetToDelete, index: index });

                let newSnippets = snippets.filter(s => s.index !== index);
                newSnippets.forEach((s, i) => s.index = i);

                if (saveSnippets(newSnippets)) {
                    showSuccessMessage('删除成功！');
                    loadSnippets();
                }
            }
        }

        // 编辑snippet
        function editSnippet(index) {
            const snippets = getSnippets();
            const snippetData = snippets.find(s => s.index === index);
            if (!snippetData) return;

            editingIndex = index;
            newSnippetTextareaTop.value = snippetData.content.replace(/<[^>]+>/g, '');
            currentTags = snippetData.tags || [];
            
            // 设置分区
            document.getElementById('sectionSelect-top').value = snippetData.section || '默认';
            
            // 展开高级功能
            document.getElementById('advanced-features-top').style.display = 'block';
            
            newSnippetTextareaTop.focus();
        }

        // 切换置顶
        function togglePin(index) {
            let snippets = getSnippets();
            const snippetIndex = snippets.findIndex(s => s.index === index);
            if (snippetIndex !== -1) {
                snippets[snippetIndex].pinned = !snippets[snippetIndex].pinned;
                if (saveSnippets(snippets)) {
                    showSuccessMessage(snippets[snippetIndex].pinned ? '已置顶' : '已取消置顶');
                    loadSnippets();
                }
            }
        }

        // 移动条目到指定分区
        function moveToSection(index, newSection) {
            let snippets = getSnippets();
            const snippetIndex = snippets.findIndex(s => s.index === index);
            if (snippetIndex !== -1) {
                const oldSection = snippets[snippetIndex].section || '默认';
                snippets[snippetIndex].section = newSection;
                if (saveSnippets(snippets)) {
                    showSuccessMessage(`条目已从 "${oldSection}" 移动到 "${newSection}"`);
                    loadSnippets();
                }
            }
        }

        // 切换展开/折叠
        function toggleExpand(button, content) {
            if (content.style.maxHeight === '80px') {
                content.style.maxHeight = 'none';
                button.innerHTML = '折叠 ▲';
            } else {
                content.style.maxHeight = '80px';
                content.style.overflow = 'hidden';
                button.innerHTML = '展开 ▼';
            }
        }

        // 全部展开/折叠
        function toggleAllExpand() {
            isAllExpanded = !isAllExpanded;
            const toggleBtn = document.getElementById('toggleAllExpandBtn');
            
            if (isAllExpanded) {
                toggleBtn.innerHTML = '📤 全部折叠';
                document.querySelectorAll('.snippet-content').forEach(content => {
                    content.style.maxHeight = 'none';
                });
                document.querySelectorAll('.toggle-expand-button').forEach(btn => {
                    btn.innerHTML = '折叠 ▲';
                });
            } else {
                toggleBtn.innerHTML = '📥 全部展开';
                document.querySelectorAll('.snippet-content').forEach(content => {
                    content.style.maxHeight = '80px';
                    content.style.overflow = 'hidden';
                });
                document.querySelectorAll('.toggle-expand-button').forEach(btn => {
                    btn.innerHTML = '展开 ▼';
                });
            }
        }

        // 智能复制
        function performSmartCopy(event, content) {
            const contentElement = event.target.closest('.snippet-content');
            if (!contentElement) return;
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            let textContent = tempDiv.textContent || tempDiv.innerText || '';
            
            // 按空格、逗号、顿号分隔文本，过滤空值
            const segments = textContent.split(/[,，、\s]+/).filter(s => s.trim().length > 0);
            
            if (segments.length === 0) return;
            
            // 获取点击位置相对于文本的偏移
            const range = document.caretRangeFromPoint(event.clientX, event.clientY);
            let clickOffset = 0;
            
            if (range && range.startContainer) {
                // 计算相对于整个文本内容的偏移
                const walker = document.createTreeWalker(
                    contentElement,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                let totalOffset = 0;
                let node;
                while (node = walker.nextNode()) {
                    if (node === range.startContainer) {
                        clickOffset = totalOffset + range.startOffset;
                        break;
                    }
                    totalOffset += node.textContent.length;
                }
            }
            
            // 在原文本中找到对应的片段
            let currentOffset = 0;
            let selectedSegment = segments[0]; // 默认选择第一个
            
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const segmentStart = textContent.indexOf(segment, currentOffset);
                const segmentEnd = segmentStart + segment.length;
                
                if (clickOffset >= segmentStart && clickOffset <= segmentEnd) {
                    selectedSegment = segment;
                    break;
                }
                currentOffset = segmentEnd;
            }
            
            // 清理文本：去掉逗号和多余空格
            const cleanText = selectedSegment.replace(/[,，]/g, '').trim();
            
            if (cleanText) {
                navigator.clipboard.writeText(cleanText)
                    .then(() => {
                        showSuccessMessage(`✅ 智能复制: "${cleanText}"`);
                        highlightTextSegment(contentElement, selectedSegment);
                    })
                    .catch(() => {
                        showSuccessMessage('❌ 复制失败');
                    });
            }
        }

        // 精确高亮被复制的文本片段
        function highlightTextSegment(contentElement, segment) {
            const originalHTML = contentElement.innerHTML;
            
            // 创建高亮的HTML
            const highlightedHTML = originalHTML.replace(
                new RegExp(`(${escapeRegExp(segment)})`, 'gi'),
                '<span class="highlighted-text">$1</span>'
            );
            
            contentElement.innerHTML = highlightedHTML;
            
            // 0.8秒后恢复原样
            setTimeout(() => {
                contentElement.innerHTML = originalHTML;
            }, 800);
        }

        // 转义正则表达式特殊字符
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // 排序功能
        function sortSnippetsData(snippets, sortType) {
            return [...snippets].sort((a, b) => {
                // 先按置顶排序
                if (a.pinned !== b.pinned) {
                    return b.pinned - a.pinned;
                }
                
                switch (sortType) {
                    case 'date-asc':
                        return a.timestamp - b.timestamp;
                    case 'date-desc':
                        return b.timestamp - a.timestamp;
                    case 'text-asc':
                        return a.content.localeCompare(b.content);
                    case 'text-desc':
                        return b.content.localeCompare(a.content);
                    case 'original':
                    default:
                        // 按原始顺序排序（最新的在前面）
                        return (a.originalIndex !== undefined ? a.originalIndex : a.index) - 
                               (b.originalIndex !== undefined ? b.originalIndex : b.index);
                }
            });
        }

        function sortSnippets(sortType) {
            currentSort = sortType;
            // 保存排序状态到localStorage
            localStorage.setItem('currentSort', sortType);
            loadSnippets();
        }

        // 增强搜索功能 - 支持内容、标签、分区搜索
        function searchSnippets() {
            currentSearchTerm = document.getElementById('searchInput').value.toLowerCase();
            loadSnippets();
        }

        // 增强的搜索匹配函数
        function matchesSearch(snippet, searchTerm) {
            if (!searchTerm) return true;

            // 搜索内容
            const content = snippet.content.toLowerCase();
            if (content.includes(searchTerm)) return true;

            // 搜索标签
            if (snippet.tags && snippet.tags.length > 0) {
                const tagsMatch = snippet.tags.some(tag =>
                    tag.toLowerCase().includes(searchTerm)
                );
                if (tagsMatch) return true;
            }

            // 搜索分区
            if (snippet.section) {
                const sectionMatch = snippet.section.toLowerCase().includes(searchTerm);
                if (sectionMatch) return true;
            }

            return false;
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            currentSearchTerm = '';
            loadSnippets();
        }

        // 分区管理
        function switchSection(section) {
            currentSection = section;
            
            // 更新标签样式
            document.querySelectorAll('.section-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-section="${section}"]`).classList.add('active');
            
            loadSnippets();
        }

        function addNewSection() {
            const sectionName = document.getElementById('newSectionName').value.trim();
            if (sectionName && !allSections.has(sectionName)) {
                allSections.add(sectionName);
                document.getElementById('newSectionName').value = '';
                
                // 保存分区信息
                if (saveSections()) {
                    updateSectionTabs();
                    updateSectionSelects();
                    showSuccessMessage('分区添加成功！');
                    
                    // 如果当前在全部视图，询问是否要切换到新分区
                    if (currentSection === 'all' && confirm(`分区"${sectionName}"创建成功！\n是否切换到该分区并开始添加条目？`)) {
                        switchSection(sectionName);
                    } else if (currentSection !== 'all') {
                        // 如果不在全部视图，建议切换到全部视图选择条目
                        if (confirm(`分区"${sectionName}"创建成功！\n\n建议切换到"全部"视图，您可以看到每个条目下方的快速分区按钮，\n轻松将现有条目添加到新分区。\n\n是否现在切换到全部视图？`)) {
                            switchSection('all');
                            
                            // 短暂延迟后显示使用提示
                            setTimeout(() => {
                                showSuccessMessage('💡 现在您可以在每个条目下方看到快速分区按钮！');
                            }, 1000);
                        }
                    }
                } else {
                    allSections.delete(sectionName); // 保存失败时回滚
                    alert('分区保存失败');
                }
            } else if (allSections.has(sectionName)) {
                alert('分区名称已存在');
            }
        }

        // 分区标签更新和管理函数
        function updateSectionTabs() {
            const sectionTabs = document.getElementById('sectionTabs');
            const snippets = getSnippets();
            
            // 计算每个分区的数量
            const sectionCounts = {};
            sectionCounts['all'] = snippets.length;
            
            allSections.forEach(section => {
                sectionCounts[section] = snippets.filter(s => (s.section || '默认') === section).length;
            });

            sectionTabs.innerHTML = '';

            // 全部标签
            const allTab = document.createElement('div');
            allTab.className = `section-tab ${currentSection === 'all' ? 'active' : ''}`;
            allTab.dataset.section = 'all';
            allTab.innerHTML = `
                <span>全部</span>
                <span class="section-count">${sectionCounts['all']}</span>
            `;
            allTab.addEventListener('click', () => switchSection('all'));
            sectionTabs.appendChild(allTab);

            // 其他分区标签
            allSections.forEach(section => {
                const tab = document.createElement('div');
                tab.className = `section-tab ${currentSection === section ? 'active' : ''}`;
                tab.dataset.section = section;
                tab.innerHTML = `
                    <span>${section}</span>
                    <span class="section-count">${sectionCounts[section] || 0}</span>
                    ${section !== '默认' ? '<span class="remove-section" style="margin-left: 8px; color: #ff3b30; cursor: pointer;">×</span>' : ''}
                `;
                tab.addEventListener('click', (e) => {
                    if (e.target.classList.contains('remove-section')) {
                        e.stopPropagation();
                        removeSection(section);
                    } else {
                        switchSection(section);
                    }
                });
                sectionTabs.appendChild(tab);
            });
        }

        function updateSectionSelects() {
            const select = document.getElementById('sectionSelect-top');
            select.innerHTML = '';
            
            allSections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                select.appendChild(option);
            });
            
            // 同时更新批量管理的分区选择器
            updateBatchTargetSection();
        }

        function removeSection(sectionName) {
            if (confirm(`确定要删除分区"${sectionName}"吗？该分区下的所有条目将移动到默认分区。`)) {
                let snippets = getSnippets();
                snippets = snippets.map(snippet => {
                    if (snippet.section === sectionName) {
                        snippet.section = '默认';
                    }
                    return snippet;
                });
                
                allSections.delete(sectionName);
                saveSnippets(snippets);
                saveSections(); // 同时保存分区信息
                
                if (currentSection === sectionName) {
                    currentSection = 'all';
                }
                
                loadSnippets();
                showSuccessMessage('分区删除成功！');
            }
        }

        function handleSectionInput(event) {
            if (event.key === 'Enter') {
                addNewSection();
            }
        }

        // 高级功能切换
        function toggleAdvancedFeatures(position) {
            const container = document.getElementById(`advanced-features-${position}`);
            if (container.style.display === 'none') {
                container.style.display = 'block';
            } else {
                container.style.display = 'none';
            }
        }

        // 标签处理
        function handleTagInput(event, position) {
            if (event.key === 'Enter') {
                const tagValue = event.target.value.trim();
                if (tagValue && !currentTags.includes(tagValue)) {
                    currentTags.push(tagValue);
                    event.target.value = '';
                    updateTagsDisplay(position);
                }
            }
        }

        function updateTagsDisplay(position) {
            const container = document.getElementById(`tags-display-${position}`);
            container.innerHTML = '';
            
            currentTags.forEach(tag => {
                const tagEl = document.createElement('span');
                tagEl.classList.add('tag');
                tagEl.textContent = tag;
                tagEl.onclick = () => removeTag(tag, position);
                container.appendChild(tagEl);
            });
        }

        function removeTag(tag, position) {
            currentTags = currentTags.filter(t => t !== tag);
            updateTagsDisplay(position);
        }

        // 深色模式切换
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
            const isDarkMode = document.body.classList.contains('dark-mode');
            localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
            document.getElementById('darkModeIcon').textContent = isDarkMode ? '☀️' : '🌙';
        }

        // 导出数据
        function exportData() {
            const snippets = localStorage.getItem('snippets');
            if (!snippets) {
                alert('没有数据可导出');
                return;
            }

            const dataObj = {
                snippets: JSON.parse(snippets),
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(dataObj, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `网络剪贴板_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            showSuccessMessage('数据导出成功！');
        }

        // 导入数据
        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (!data.snippets) {
                        throw new Error('导入的文件格式无效');
                    }

                    if (confirm('导入将覆盖当前数据，确定继续吗？')) {
                        localStorage.setItem('snippets', JSON.stringify(data.snippets));
                        loadSnippets();
                        showSuccessMessage('数据导入成功！');
                    }
                } catch (error) {
                    alert('导入失败: ' + error.message);
                }
            };
            reader.readAsText(file);
            event.target.value = '';
        }

        // 成功消息
        function showSuccessMessage(message) {
            const messageEl = document.createElement('div');
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: var(--button-success);
                color: white;
                padding: 15px 30px;
                border-radius: 12px;
                z-index: 1000;
                box-shadow: 0 6px 16px rgba(0,0,0,0.2);
                animation: successMessage 1.8s ease forwards;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes successMessage {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    15% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
                    20% { transform: translate(-50%, -50%) scale(1); }
                    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(messageEl);

            setTimeout(() => {
                document.body.removeChild(messageEl);
                document.head.removeChild(style);
            }, 1800);
        }

        // 下载文件
        function downloadFile(fileElement) {
            try {
                const fileData = JSON.parse(fileElement.dataset.file);
                const link = document.createElement('a');
                link.href = fileData.data;
                link.download = fileData.name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                alert('下载失败: ' + error.message);
            }
        }

        // 数据恢复相关函数
        function showDataRecovery() {
            document.getElementById('dataRecoveryPanel').style.display = 'block';
        }

        function hideDataRecovery() {
            document.getElementById('dataRecoveryPanel').style.display = 'none';
        }

        function checkLocalStorageData() {
            const rawData = localStorage.getItem('snippets');
            if (rawData) {
                try {
                    const parsedData = JSON.parse(rawData);
                    alert(`找到 ${parsedData.length} 条本地数据记录\n\n点击"查看原始数据"可以看到详细内容`);
                } catch (error) {
                    alert('本地数据格式异常：' + error.message);
                }
            } else {
                alert('未找到本地数据，可能已被清空');
            }
        }

        function restoreFromBackup() {
            const backupKeys = ['snippets_backup', 'snippets_backup_auto', 'snippets_backup_before_import'];
            let restored = false;
            
            for (const key of backupKeys) {
                const backup = localStorage.getItem(key);
                if (backup) {
                    try {
                        const backupData = JSON.parse(backup);
                        if (confirm(`找到备份数据 ${backupData.length} 条，是否恢复？\n备份来源：${key}`)) {
                            localStorage.setItem('snippets', backup);
                            loadSnippets();
                            showSuccessMessage('数据恢复成功！');
                            hideDataRecovery();
                            restored = true;
                            break;
                        }
                    } catch (error) {
                        console.error(`备份 ${key} 损坏:`, error);
                    }
                }
            }
            
            if (!restored) {
                alert('未找到可用的备份数据');
            }
        }

        function showRawData() {
            const rawData = localStorage.getItem('snippets');
            if (rawData) {
                const newWindow = window.open('', '_blank');
                newWindow.document.write(`
                    <html>
                    <head><title>原始数据查看</title></head>
                    <body style="font-family: monospace; padding: 20px;">
                        <h2>localStorage中的原始数据：</h2>
                        <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">${rawData}</pre>
                    </body>
                    </html>
                `);
                newWindow.document.close();
            } else {
                alert('未找到本地数据');
            }
        }

        function clearAndReset() {
            if (confirm('⚠️ 警告：这将清除所有数据！\n\n确定要清空并重置吗？此操作不可撤销！')) {
                localStorage.removeItem('snippets');
                localStorage.removeItem('snippets_backup');
                localStorage.removeItem('snippets_backup_auto');
                loadSnippets();
                showSuccessMessage('数据已清空');
                hideDataRecovery();
            }
        }

        // 批量管理功能
        function toggleBatchMode() {
            batchMode = !batchMode;
            selectedIndices.clear();
            
            const btn = document.getElementById('batchModeBtn');
            const actions = document.getElementById('batchActions');
            const counter = document.getElementById('batchCounter');
            
            if (batchMode) {
                btn.textContent = '关闭批量选择';
                btn.style.background = '#ff9500';
                actions.style.display = 'flex';
            } else {
                btn.textContent = '开启批量选择';
                btn.style.background = 'var(--button-info)';
                actions.style.display = 'none';
            }
            
            counter.textContent = '已选择 0 项';
            loadSnippets();
        }

        function updateBatchTargetSection() {
            const select = document.getElementById('batchTargetSection');
            select.innerHTML = '<option value="">选择目标分区</option>';
            
            allSections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                select.appendChild(option);
            });
        }

        function toggleSnippetSelection(index) {
            if (selectedIndices.has(index)) {
                selectedIndices.delete(index);
            } else {
                selectedIndices.add(index);
            }
            
            updateBatchCounter();
            updateSnippetSelectionUI(index);
        }

        function updateBatchCounter() {
            const counter = document.getElementById('batchCounter');
            counter.textContent = `已选择 ${selectedIndices.size} 项`;
        }

        function updateSnippetSelectionUI(index) {
            const snippet = document.querySelector(`[data-index="${index}"]`);
            if (snippet) {
                if (selectedIndices.has(index)) {
                    snippet.style.background = 'rgba(0, 123, 255, 0.1)';
                    snippet.style.border = '2px solid var(--highlight-color)';
                } else {
                    snippet.style.background = '';
                    snippet.style.border = '';
                }
            }
        }

        function batchMoveToSection() {
            const targetSection = document.getElementById('batchTargetSection').value;
            if (!targetSection) {
                alert('请选择目标分区');
                return;
            }
            
            if (selectedIndices.size === 0) {
                alert('请先选择要移动的条目');
                return;
            }
            
            let snippets = getSnippets();
            let moveCount = 0;
            
            selectedIndices.forEach(index => {
                const snippetIndex = snippets.findIndex(s => s.index === index);
                if (snippetIndex !== -1) {
                    snippets[snippetIndex].section = targetSection;
                    moveCount++;
                }
            });
            
            if (saveSnippets(snippets)) {
                showSuccessMessage(`成功移动 ${moveCount} 个条目到 "${targetSection}"`);
                selectedIndices.clear();
                loadSnippets();
                updateBatchCounter();
            }
        }

        function batchDelete() {
            if (selectedIndices.size === 0) {
                alert('请先选择要删除的条目');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${selectedIndices.size} 个条目吗？此操作不可撤销！`)) {
                let snippets = getSnippets();
                snippets = snippets.filter(s => !selectedIndices.has(s.index));
                
                // 重新编号
                snippets.forEach((s, i) => s.index = i);
                
                if (saveSnippets(snippets)) {
                    showSuccessMessage(`成功删除 ${selectedIndices.size} 个条目`);
                    selectedIndices.clear();
                    loadSnippets();
                    updateBatchCounter();
                }
            }
        }

        // 通用折叠功能
        function toggleCollapsibleSection(sectionName) {
            const content = document.getElementById(sectionName + 'Content');
            const toggle = document.getElementById(sectionName + 'Toggle');
            const isCollapsed = content.classList.contains('collapsed');
            
            if (isCollapsed) {
                content.classList.remove('collapsed');
                content.style.maxHeight = content.scrollHeight + 'px';
                toggle.textContent = '🔽';
                toggle.classList.remove('collapsed');
                localStorage.setItem(sectionName + 'Collapsed', 'false');
            } else {
                content.style.maxHeight = content.scrollHeight + 'px';
                content.offsetHeight;
                content.style.maxHeight = '0';
                content.classList.add('collapsed');
                toggle.textContent = '🔼';
                toggle.classList.add('collapsed');
                localStorage.setItem(sectionName + 'Collapsed', 'true');
            }
        }

        // 初始化所有折叠区域
        function initCollapsibleSections() {
            const sections = [
                { name: 'toolbox', defaultCollapsed: true },    // 功能面板默认折叠
                { name: 'addContent', defaultCollapsed: false }  // 添加内容默认展开
            ];
            
            sections.forEach(section => {
                const savedState = localStorage.getItem(section.name + 'Collapsed');
                const content = document.getElementById(section.name + 'Content');
                const toggle = document.getElementById(section.name + 'Toggle');
                
                // 如果没有保存状态，使用默认状态
                if (savedState === null) {
                    localStorage.setItem(section.name + 'Collapsed', section.defaultCollapsed.toString());
                }
                
                const shouldCollapse = localStorage.getItem(section.name + 'Collapsed') === 'true';
                
                if (shouldCollapse) {
                    content.classList.add('collapsed');
                    content.style.maxHeight = '0';
                    toggle.textContent = '🔼';
                    toggle.classList.add('collapsed');
                } else {
                    setTimeout(() => {
                        content.style.maxHeight = content.scrollHeight + 'px';
                    }, 100);
                }
            });
        }

        // 显示控制功能
        function toggleSectionInfo() {
            showSectionInfo = !showSectionInfo;
            localStorage.setItem('showSectionInfo', showSectionInfo.toString());
            loadSnippets();
        }

        function toggleQuickSections() {
            showQuickSections = !showQuickSections;
            localStorage.setItem('showQuickSections', showQuickSections.toString());
            loadSnippets();
        }

        // 初始化显示设置
        function initDisplaySettings() {
            // 从localStorage加载设置
            showSectionInfo = localStorage.getItem('showSectionInfo') === 'true';
            showQuickSections = localStorage.getItem('showQuickSections') === 'true';
            
            // 设置复选框状态
            const sectionInfoCheck = document.getElementById('showSectionInfoCheck');
            const quickSectionsCheck = document.getElementById('showQuickSectionsCheck');
            
            if (sectionInfoCheck) sectionInfoCheck.checked = showSectionInfo;
            if (quickSectionsCheck) quickSectionsCheck.checked = showQuickSections;
        }

        // 悬停时高亮当前文本片段
        function highlightHoverSegment(event, content, contentElement) {
            // 清除之前的高亮
            clearHoverHighlight(contentElement);
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            let textContent = tempDiv.textContent || tempDiv.innerText || '';
            
            // 按空格、逗号、顿号分隔文本
            const segments = textContent.split(/[,，、\s]+/).filter(s => s.trim().length > 0);
            if (segments.length === 0) return;
            
            // 获取点击位置
            const range = document.caretRangeFromPoint(event.clientX, event.clientY);
            let clickOffset = 0;
            
            if (range && range.startContainer) {
                const walker = document.createTreeWalker(
                    contentElement,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                let totalOffset = 0;
                let node;
                while (node = walker.nextNode()) {
                    if (node === range.startContainer) {
                        clickOffset = totalOffset + range.startOffset;
                        break;
                    }
                    totalOffset += node.textContent.length;
                }
            }
            
            // 找到对应的片段
            let currentOffset = 0;
            let selectedSegment = null;
            
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const segmentStart = textContent.indexOf(segment, currentOffset);
                const segmentEnd = segmentStart + segment.length;
                
                if (clickOffset >= segmentStart && clickOffset <= segmentEnd) {
                    selectedSegment = segment;
                    break;
                }
                currentOffset = segmentEnd;
            }
            
            // 高亮当前片段
            if (selectedSegment && selectedSegment.trim()) {
                const originalHTML = contentElement.innerHTML;
                const highlightedHTML = originalHTML.replace(
                    new RegExp(`(${escapeRegExp(selectedSegment)})`, 'gi'),
                    '<span class="hover-segment">$1</span>'
                );
                contentElement.innerHTML = highlightedHTML;
                contentElement.dataset.originalHtml = originalHTML;
            }
        }

        // 清除悬停高亮
        function clearHoverHighlight(contentElement) {
            if (contentElement.dataset.originalHtml) {
                contentElement.innerHTML = contentElement.dataset.originalHtml;
                delete contentElement.dataset.originalHtml;
            }
        }

        // ⌨️ 键盘快捷键支持
        function initKeyboardShortcuts() {
            document.addEventListener('keydown', function(event) {
                // Ctrl + Enter: 快速添加
                if (event.ctrlKey && event.key === 'Enter') {
                    event.preventDefault();
                    if (newSnippetTextareaTop.value.trim()) {
                        addSnippet('top');
                    } else {
                        newSnippetTextareaTop.focus();
                    }
                }

                // Ctrl + F: 搜索
                if (event.ctrlKey && event.key === 'f') {
                    event.preventDefault();
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }

                // Ctrl + D: 切换深色模式
                if (event.ctrlKey && event.key === 'd') {
                    event.preventDefault();
                    toggleDarkMode();
                }

                // Ctrl + A: 全选（在批量模式下）
                if (event.ctrlKey && event.key === 'a' && batchMode) {
                    event.preventDefault();
                    selectAllSnippets();
                }

                // Ctrl + B: 切换批量模式
                if (event.ctrlKey && event.key === 'b') {
                    event.preventDefault();
                    toggleBatchMode();
                }

                // Delete: 删除选中的项目
                if (event.key === 'Delete') {
                    if (batchMode && selectedIndices.size > 0) {
                        event.preventDefault();
                        batchDelete();
                    }
                }

                // Ctrl + Z: 撤销
                if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
                    event.preventDefault();
                    undo();
                }

                // Ctrl + Shift + Z 或 Ctrl + Y: 重做
                if (event.ctrlKey && ((event.key === 'z' && event.shiftKey) || event.key === 'y')) {
                    event.preventDefault();
                    redo();
                }

                // Ctrl + S: 导出数据
                if (event.ctrlKey && event.key === 's') {
                    event.preventDefault();
                    exportData();
                }

                // Escape: 退出批量模式或清除搜索
                if (event.key === 'Escape') {
                    if (batchMode) {
                        toggleBatchMode();
                    } else if (currentSearchTerm) {
                        clearSearch();
                    } else if (editingIndex !== -1) {
                        // 取消编辑
                        editingIndex = -1;
                        newSnippetTextareaTop.value = '';
                        showSuccessMessage('已取消编辑');
                    }
                }

                // F1: 显示/隐藏快捷键提示
                if (event.key === 'F1') {
                    event.preventDefault();
                    toggleKeyboardHint();
                }

                // 数字键 1-9: 快速切换分区
                if (event.ctrlKey && event.key >= '1' && event.key <= '9') {
                    event.preventDefault();
                    const sectionIndex = parseInt(event.key) - 1;
                    const sections = [...allSections];
                    if (sectionIndex < sections.length) {
                        switchSection(sections[sectionIndex]);
                        showSuccessMessage(`切换到分区: ${sections[sectionIndex]}`);
                    }
                }
            });
        }

        // 显示键盘快捷键提示
        function showKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            if (hint) {
                hint.classList.add('show');
            }
        }

        // 隐藏键盘快捷键提示
        function hideKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            if (hint) {
                hint.classList.remove('show');
            }
        }

        // 切换键盘快捷键提示显示状态
        function toggleKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            if (hint) {
                hint.classList.toggle('show');
            }
        }

        // 全选所有片段（批量模式下）
        function selectAllSnippets() {
            if (!batchMode) return;

            const snippets = getSnippets();
            let filteredSnippets = snippets;

            // 应用当前的过滤条件
            if (currentSection !== 'all') {
                filteredSnippets = snippets.filter(snippet =>
                    (snippet.section || '默认') === currentSection
                );
            }

            if (currentSearchTerm) {
                filteredSnippets = filteredSnippets.filter(snippet =>
                    snippet.content.toLowerCase().includes(currentSearchTerm)
                );
            }

            // 选择所有当前显示的片段
            filteredSnippets.forEach(snippet => {
                selectedIndices.add(snippet.index);
            });

            loadSnippets(); // 重新渲染以显示选择状态
        }

        // 清除搜索
        function clearSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
                currentSearchTerm = '';
                loadSnippets();
            }
        }

        // 优化搜索功能 - 添加实时搜索
        function initEnhancedSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                let searchTimeout;

                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        currentSearchTerm = this.value.toLowerCase();
                        loadSnippets();
                    }, 300); // 300ms 防抖
                });

                // 支持 Enter 键搜索
                searchInput.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter') {
                        currentSearchTerm = this.value.toLowerCase();
                        loadSnippets();
                    }
                });
            }
        }

        // 在页面加载完成后初始化增强搜索
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initEnhancedSearch, 100);
        });

        // 🎯 拖拽排序功能
        function setupDragEvents(element, index) {
            element.addEventListener('dragstart', function(e) {
                draggedElement = element;
                draggedIndex = index;
                element.classList.add('dragging');

                // 设置拖拽数据
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', element.outerHTML);

                // 创建拖拽时的视觉反馈
                setTimeout(() => {
                    element.style.opacity = '0.5';
                }, 0);
            });

            element.addEventListener('dragend', function(e) {
                element.classList.remove('dragging');
                element.style.opacity = '';

                // 清理所有拖拽状态
                document.querySelectorAll('.snippet').forEach(snippet => {
                    snippet.classList.remove('drag-over');
                });

                draggedElement = null;
                draggedIndex = -1;
            });

            element.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                if (draggedElement && draggedElement !== element) {
                    element.classList.add('drag-over');
                }
            });

            element.addEventListener('dragleave', function(e) {
                element.classList.remove('drag-over');
            });

            element.addEventListener('drop', function(e) {
                e.preventDefault();
                element.classList.remove('drag-over');

                if (draggedElement && draggedElement !== element) {
                    const targetIndex = parseInt(element.dataset.index);
                    reorderSnippets(draggedIndex, targetIndex);
                }
            });
        }

        // 重新排序snippets
        function reorderSnippets(fromIndex, toIndex) {
            if (fromIndex === toIndex) return;

            let snippets = getSnippets();

            // 找到要移动的元素
            const fromSnippet = snippets.find(s => s.index === fromIndex);
            const toSnippet = snippets.find(s => s.index === toIndex);

            if (!fromSnippet || !toSnippet) return;

            // 移除要移动的元素
            snippets = snippets.filter(s => s.index !== fromIndex);

            // 找到目标位置并插入
            const targetPosition = snippets.findIndex(s => s.index === toIndex);
            snippets.splice(targetPosition, 0, fromSnippet);

            // 重新分配索引
            snippets.forEach((snippet, index) => {
                snippet.index = index;
            });

            // 保存并重新加载
            if (saveSnippets(snippets)) {
                showSuccessMessage('🎯 排序已更新！');
                loadSnippets();
            }
        }

        // 🎨 动画增强函数
        function addButtonClickAnimation(button) {
            button.classList.add('button-clicked');
            setTimeout(() => {
                button.classList.remove('button-clicked');
            }, 300);
        }

        function addErrorShakeAnimation(element) {
            element.classList.add('error-shake');
            setTimeout(() => {
                element.classList.remove('error-shake');
            }, 500);
        }

        function addSearchHighlight(element) {
            element.classList.add('search-highlight');
            setTimeout(() => {
                element.classList.remove('search-highlight');
            }, 2000);
        }

        // 增强成功消息显示
        function showSuccessMessage(message) {
            // 创建成功消息元素
            const messageEl = document.createElement('div');
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
            `;
            messageEl.classList.add('success-message');

            document.body.appendChild(messageEl);

            // 3秒后自动移除
            setTimeout(() => {
                messageEl.style.animation = 'slideInLeft 0.3s ease-out reverse';
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }, 3000);
        }

        // 🎨 主题系统函数
        function showThemeSelector() {
            const panel = document.getElementById('themePanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';

                // 高亮当前主题
                const currentTheme = localStorage.getItem('currentTheme') || 'default';
                document.querySelectorAll('.theme-option').forEach(option => {
                    option.classList.remove('active');
                    if (option.dataset.theme === currentTheme) {
                        option.classList.add('active');
                    }
                });
            }
        }

        function hideThemeSelector() {
            const panel = document.getElementById('themePanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        function applyTheme(themeName) {
            // 移除所有主题类
            document.body.classList.remove('theme-sunset', 'theme-forest', 'theme-ocean', 'theme-purple', 'theme-rose');

            // 应用新主题
            if (themeName !== 'default') {
                document.body.classList.add(`theme-${themeName}`);
            }

            // 保存主题设置
            localStorage.setItem('currentTheme', themeName);

            // 更新主题选择器状态
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
                if (option.dataset.theme === themeName) {
                    option.classList.add('active');
                }
            });

            // 显示成功消息
            const themeNames = {
                'default': '🌟 默认主题',
                'sunset': '🌅 日落主题',
                'forest': '🌲 森林主题',
                'ocean': '🌊 海洋主题',
                'purple': '💜 紫罗兰主题',
                'rose': '🌹 玫瑰主题'
            };
            showSuccessMessage(`已切换到 ${themeNames[themeName]}`);

            // 自动关闭主题选择器
            setTimeout(() => {
                hideThemeSelector();
            }, 1000);
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('currentTheme');
            if (savedTheme && savedTheme !== 'default') {
                applyTheme(savedTheme);
            }
        }

        // 在页面加载时初始化主题
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
        });

        // 🔄 撤销重做系统
        function saveStateForUndo(action, data) {
            const state = {
                action: action,
                data: JSON.parse(JSON.stringify(data)), // 深拷贝
                timestamp: Date.now(),
                snippets: JSON.parse(JSON.stringify(getSnippets())) // 保存当前状态
            };

            undoStack.push(state);

            // 限制撤销栈大小
            if (undoStack.length > MAX_UNDO_STEPS) {
                undoStack.shift();
            }

            // 清空重做栈
            redoStack = [];

            updateUndoRedoButtons();
        }

        function undo() {
            if (undoStack.length === 0) {
                showSuccessMessage('没有可撤销的操作');
                return;
            }

            const currentState = {
                action: 'current',
                data: null,
                timestamp: Date.now(),
                snippets: JSON.parse(JSON.stringify(getSnippets()))
            };

            redoStack.push(currentState);

            const lastState = undoStack.pop();

            // 恢复到上一个状态
            if (saveSnippets(lastState.snippets)) {
                showSuccessMessage(`✅ 已撤销: ${getActionName(lastState.action)}`);
                loadSnippets();
                updateUndoRedoButtons();
            }
        }

        function redo() {
            if (redoStack.length === 0) {
                showSuccessMessage('没有可重做的操作');
                return;
            }

            const currentState = {
                action: 'current',
                data: null,
                timestamp: Date.now(),
                snippets: JSON.parse(JSON.stringify(getSnippets()))
            };

            undoStack.push(currentState);

            const nextState = redoStack.pop();

            // 恢复到下一个状态
            if (saveSnippets(nextState.snippets)) {
                showSuccessMessage(`✅ 已重做操作`);
                loadSnippets();
                updateUndoRedoButtons();
            }
        }

        function getActionName(action) {
            const actionNames = {
                'add': '添加内容',
                'delete': '删除内容',
                'edit': '编辑内容',
                'move': '移动内容',
                'pin': '置顶操作',
                'batch_delete': '批量删除',
                'batch_move': '批量移动',
                'reorder': '重新排序'
            };
            return actionNames[action] || '未知操作';
        }

        function updateUndoRedoButtons() {
            // 这里可以更新撤销重做按钮的状态
            // 如果有UI按钮的话
        }

        // 修改现有的操作函数，添加撤销支持
        function addSnippetWithUndo(position) {
            const beforeState = getSnippets();
            addSnippet(position);
            const afterState = getSnippets();

            if (afterState.length > beforeState.length) {
                saveStateForUndo('add', { position: position });
            }
        }

        function deleteSnippetWithUndo(index) {
            const snippets = getSnippets();
            const snippetToDelete = snippets.find(s => s.index === index);

            if (snippetToDelete && confirm('确定要删除这条记录吗？')) {
                saveStateForUndo('delete', { snippet: snippetToDelete, index: index });

                let newSnippets = snippets.filter(s => s.index !== index);
                newSnippets.forEach((s, i) => s.index = i);

                if (saveSnippets(newSnippets)) {
                    showSuccessMessage('删除成功！');
                    loadSnippets();
                }
            }
        }
    </script>
</body>
</html> 